# Tech Context

## Technology Stack

### Frontend
- **React** 18.x - UI library
- **Vite** - Build tool and dev server
- **Tailwind CSS** - Utility-first CSS framework
- **React Router** - Client-side routing
- **Axios** - HTTP client

### Backend
- **Python** 3.x
- **Flask** 3.x - Web framework
- **SQLAlchemy** - ORM
- **Flask-JWT-Extended** - JWT authentication
- **Flask-CORS** - CORS handling
- **Flask-Migrate** - Database migrations
- **Gunicorn** - WSGI server

### Development Tools
- **Node.js** & npm - Frontend tooling
- **Python pip** - Backend dependencies
- **systemd** - Service management

## Environment Setup

### Frontend Environment Variables
- `VITE_API_URL` - Backend API endpoint

### Backend Environment Variables
- `SECRET_KEY` - Flask secret key
- `JWT_SECRET_KEY` - JWT signing key
- `DATABASE_URL` - Database connection string
- `WEATHER_API_KEY` - OpenWeatherMap API key

## Constraints and Considerations

1. **Browser Support**: Modern browsers only (ES6+ support required)
2. **Database**: SQLite for development, PostgreSQL for production
3. **API Rate Limits**: Weather API has rate limits
4. **Session Management**: JWT tokens expire after 24 hours
5. **File Structure**: Flat file structure with descriptive naming
6. **Deployment**: Linux-based deployment with systemd

## Development Workflow

1. Frontend runs on port 5173 (Vite default)
2. Backend runs on port 5000 (Flask default)
3. Proxy configuration in Vite for API calls
4. Hot module replacement for frontend development
5. Flask debug mode for backend development
6. CLI utilities for user management:
   - `flask create-user <username>` - Create new user account
   - `flask reset-password <username>` - Reset user password