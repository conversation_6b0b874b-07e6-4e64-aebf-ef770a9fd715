"""Shared response utility functions."""

from flask import jsonify


def json_error(msg: str, code: int = 400):
    """Standard JSON error response.
    
    Args:
        msg: Error message to include in response
        code: HTTP status code (default: 400)
        
    Returns:
        tuple: (response, status_code) for Flask
    """
    return jsonify({"error": msg}), code


def json_success(data=None, message=None, code: int = 200):
    """Standard JSON success response.
    
    Args:
        data: Data to include in response
        message: Success message (optional)
        code: HTTP status code (default: 200)
        
    Returns:
        tuple: (response, status_code) for Flask
    """
    response = {"success": True}
    if data is not None:
        response["data"] = data
    if message:
        response["message"] = message
    return jsonify(response), code