"""Database connection validation and utilities."""

from typing import Any
from pymongo.errors import ServerSelectionTimeoutError, ConnectionFailure
from flask import current_app

from app.extensions import mongo


class DatabaseConnectionError(Exception):
    """Raised when database connection is unavailable."""
    pass


class DeletionPreventedError(Exception):
    """Raised when deletion is prevented due to referential integrity constraints."""
    pass


def validate_connection() -> None:
    """Validate that MongoDB connection is available.
    
    Raises:
        DatabaseConnectionError: If connection is not available.
    """
    try:
        # Ping the database with a short timeout
        mongo.cx.admin.command("ping", maxTimeMS=5000)
    except (ServerSelectionTimeoutError, ConnectionFailure) as exc:
        current_app.logger.error("MongoDB connection failed: %s", exc)
        raise DatabaseConnectionError("Database connection is unavailable") from exc
    except Exception as exc:
        current_app.logger.error("Unexpected database error: %s", exc)
        raise DatabaseConnectionError("Database error occurred") from exc


def safe_operation(operation_func, default_value: Any = None, reraise: bool = True):
    """Execute a database operation with connection error handling.
    
    Args:
        operation_func: Function to execute (should return a value)
        default_value: Value to return if operation fails and reraise=False
        reraise: Whether to reraise DatabaseConnectionError
        
    Returns:
        Result of operation_func or default_value
        
    Raises:
        DatabaseConnectionError: If connection fails and reraise=True
    """
    try:
        return operation_func()
    except (ServerSelectionTimeoutError, ConnectionFailure) as exc:
        current_app.logger.error("Database connection failed during operation: %s", exc)
        error = DatabaseConnectionError("Database connection is unavailable")
        if reraise:
            raise error from exc
        current_app.logger.warning("Database operation failed, returning default value")
        return default_value
    except Exception as exc:
        current_app.logger.error("Unexpected error during operation: %s", exc)
        # Don't mask programming errors - re-raise unless explicitly suppressed
        if reraise:
            raise
        current_app.logger.warning("Operation failed, returning default value")
        return default_value