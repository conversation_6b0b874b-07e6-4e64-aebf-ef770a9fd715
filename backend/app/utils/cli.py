"""Command-line interface (CLI) utilities for the application."""

import click
from flask.cli import with_appcontext

from app.models.user import User


def register_cli_commands(app):
    """Register CLI commands with the Flask app."""

    @app.cli.command("create-user")
    @click.argument("username")
    @click.password_option()
    @click.option("--email", help="User's email address.")
    @with_appcontext
    def create_user(username, password, email):
        """Create a new user for the application.

        This is intended for initial setup in a development environment,
        as the application is designed for a single user.
        """
        if User.by_username(username):
            click.echo(f"Error: User '{username}' already exists.")
            return

        try:
            user = User.create(username=username, password=password, email=email)
            click.echo(f"User '{user.username}' created successfully with ID: {user.id}")
        except Exception as e:
            click.echo(f"Error creating user: {e}")

    @app.cli.command("reset-password")
    @click.argument("username")
    @click.password_option()
    @with_appcontext
    def reset_password(username, password):
        """Reset the password for an existing user."""
        user = User.by_username(username)
        if not user:
            click.echo(f"Error: User '{username}' not found.")
            return

        try:
            user.set_password(password)
            click.echo(f"Password for user '{username}' has been reset.")
        except Exception as e:
            click.echo(f"Error resetting password: {e}")