"""Authorization utilities and decorators."""

from functools import wraps
from typing import Any, Callable

from bson import ObjectId
from flask import request
from flask_login import current_user
from werkzeug.exceptions import NotFound

from app.utils.responses import json_error


def require_ownership(model_class, param_name: str = None):
    """Decorator that fetches an object and validates user ownership.
    
    Args:
        model_class: The model class (e.g., Bill, Contact, Event, Todo)
        param_name: URL parameter name (defaults to f"{model_class.__name__.lower()}_id")
    
    Usage:
        @bp.patch("/<bill_id>")
        @login_required
        @require_ownership(Bill)
        def update_bill(bill_id: str, bill):
            # bill is the fetched and validated object
            pass
    """
    if param_name is None:
        param_name = f"{model_class.__name__.lower()}_id"
    
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Get the object ID from route parameters
            object_id = kwargs.get(param_name)
            if not object_id:
                # Try to get from request view_args as fallback
                object_id = request.view_args.get(param_name)
            
            if not object_id:
                return json_error(f"Missing {param_name} parameter", 400)
            
            # Fetch the object
            obj = model_class.get(object_id)
            if obj is None:
                raise NotFound(f"{model_class.__name__} not found")
            
            # Check ownership
            if obj.user_id != current_user.id:
                return json_error("Forbidden", 403)
            
            # Add the fetched object to kwargs
            object_param_name = model_class.__name__.lower()
            kwargs[object_param_name] = obj
            
            return func(*args, **kwargs)
        
        return wrapper
    return decorator