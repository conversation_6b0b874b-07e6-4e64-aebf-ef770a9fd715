from __future__ import annotations

"""To‑Do model helper wrapping the `todos` collection.

Each item is a simple text entry with `created_at` and optional
`completed_at`.  Once marked complete the front‑end will remove it immediately;
however, the document remains until the TTL index (24 h) purges it.
"""

from datetime import datetime, timezone
from typing import Any, Dict, List, Optional

from bson.objectid import ObjectId

from app.extensions import mongo
from app.utils.database import safe_operation, DatabaseConnectionError

COLL = mongo.db.todos


class Todo:
    """Lightweight adapter around a to‑do document."""

    def __init__(self, doc: Dict[str, Any]):
        self._doc = doc
        self.id: str = str(doc["_id"])

    # ------------------------------------------------------------------
    # Creation & retrieval
    # ------------------------------------------------------------------

    @classmethod
    def create(cls, *, user_id: str | ObjectId, text: str) -> "Todo":
        now = datetime.utcnow().replace(tzinfo=timezone.utc)
        doc = {
            "user_id": ObjectId(user_id),
            "text": text,
            "created_at": now,
            "completed_at": None,
        }
        def _insert():
            COLL.insert_one(doc)
            return cls(doc)
        return safe_operation(_insert)

    @classmethod
    def get(cls, todo_id: str | ObjectId) -> "Todo | None":
        def _query():
            obj_id = ObjectId(todo_id) if isinstance(todo_id, str) else todo_id
            if (doc := COLL.find_one({"_id": obj_id})) is not None:
                return cls(doc)
            return None
        return safe_operation(_query, default_value=None)

    @classmethod
    def list_by_user(cls, user_id: str | ObjectId, *, include_completed: bool = False) -> List["Todo"]:
        def _query():
            query: Dict[str, Any] = {"user_id": ObjectId(user_id)}
            if not include_completed:
                query["completed_at"] = None
            cursor = COLL.find(query).sort("created_at", -1)
            return [cls(doc) for doc in cursor]
        
        return safe_operation(_query, default_value=[])

    # ------------------------------------------------------------------
    # Instance mutators
    # ------------------------------------------------------------------

    def delete(self):
        def _delete():
            COLL.delete_one({"_id": ObjectId(self.id)})
        safe_operation(_delete)

    def update(self, **fields):
        """Update todo with provided fields in a single operation."""
        if not fields:
            return
        
        # Build the update operations
        update_ops = {}
        unset_ops = {}
        
        for field, value in fields.items():
            if field == "text":
                update_ops["text"] = value.strip() if isinstance(value, str) else value
            elif field == "completed":
                if value:
                    update_ops["completed_at"] = datetime.utcnow().replace(tzinfo=timezone.utc)
                else:
                    unset_ops["completed_at"] = ""
            else:
                update_ops[field] = value
        
        # Execute the update
        def _update():
            update_doc = {}
            if update_ops:
                update_doc["$set"] = update_ops
            if unset_ops:
                update_doc["$unset"] = unset_ops
            
            if update_doc:
                COLL.update_one({"_id": ObjectId(self.id)}, update_doc)
                # Update local document
                for field, value in update_ops.items():
                    self._doc[field] = value
                for field in unset_ops:
                    self._doc[field] = None
        
        safe_operation(_update)

    # ------------------------------------------------------------------
    # Model properties (for encapsulation)
    # ------------------------------------------------------------------

    @property
    def user_id(self) -> str:
        """Get the user ID that owns this todo."""
        return str(self._doc["user_id"])

    # ------------------------------------------------------------------
    # Serialisation
    # ------------------------------------------------------------------

    def to_dict(self) -> Dict[str, Any]:
        return {
            "id": self.id,
            "text": self._doc["text"],
            "created_at": self._doc["created_at"].isoformat(),
            "completed_at": (
                self._doc["completed_at"].isoformat() if self._doc.get("completed_at") else None
            ),
        }
