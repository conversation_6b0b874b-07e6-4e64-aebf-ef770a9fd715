from __future__ import annotations

"""Contact model helper wrapping the `contacts` collection."""

from datetime import datetime, timezone
from typing import Any, Dict, List, Optional

from bson.objectid import ObjectId
from flask import current_app

from app.extensions import mongo
from app.models.contact_type import ContactType  
from app.utils.database import safe_operation, DatabaseConnectionError

COLL = mongo.db.contacts


class Contact:
    """Adapter around a contact document."""

    def __init__(self, doc: Dict[str, Any]):
        self._doc = doc
        self.id: str = str(doc["_id"])

    # ------------------------------------------------------------------
    # Creation & retrieval
    # ------------------------------------------------------------------

    @classmethod
    def create(
        cls,
        *,
        user_id: str | ObjectId,
        type_id: str | ObjectId,
        name: str,
        last_contact: Optional[datetime] = None,
    ) -> "Contact":
        last_contact = last_contact or datetime.utcnow().replace(tzinfo=timezone.utc)
        doc = {
            "user_id": ObjectId(user_id),
            "type_id": ObjectId(type_id),
            "name": name.strip(),
            "last_contact": last_contact,
        }
        def _insert():
            COLL.insert_one(doc)
            return cls(doc)
        return safe_operation(_insert)

    @classmethod
    def get(cls, contact_id: str | ObjectId) -> "Contact | None":
        def _query():
            obj_id = ObjectId(contact_id) if isinstance(contact_id, str) else contact_id
            if (doc := COLL.find_one({"_id": obj_id})) is not None:
                return cls(doc)
            return None
        return safe_operation(_query, default_value=None)

    @classmethod
    def list_by_user(cls, user_id: str | ObjectId) -> List["Contact"]:
        def _query():
            # Use aggregation pipeline with $lookup to fetch contact_type data in one query
            pipeline = [
                {"$match": {"user_id": ObjectId(user_id)}},
                {"$lookup": {
                    "from": "contact_types",
                    "localField": "type_id",
                    "foreignField": "_id",
                    "as": "contact_type"
                }},
                {"$sort": {"name": 1}}
            ]
            cursor = COLL.aggregate(pipeline)
            contacts = []
            for doc in cursor:
                # Embed contact_type data to avoid separate lookups in status calculation
                if doc.get("contact_type") and len(doc["contact_type"]) > 0:
                    doc["_contact_type_threshold"] = doc["contact_type"][0].get("threshold_days", 7)
                else:
                    doc["_contact_type_threshold"] = 7  # fallback
                    doc["_contact_type_missing"] = True  # flag for missing ContactType
                contacts.append(cls(doc))
            return contacts
        
        return safe_operation(_query, default_value=[])

    # ------------------------------------------------------------------
    # Instance mutators
    # ------------------------------------------------------------------

    def delete(self):
        def _delete():
            COLL.delete_one({"_id": ObjectId(self.id)})
        safe_operation(_delete)

    def ping(self):
        """Update last_contact timestamp to now."""
        now = datetime.utcnow().replace(tzinfo=timezone.utc)
        def _ping():
            COLL.update_one({"_id": ObjectId(self.id)}, {"$set": {"last_contact": now}})
            self._doc["last_contact"] = now
        safe_operation(_ping)

    def update_type(self, new_type_id: str | ObjectId):
        def _update_type():
            COLL.update_one({"_id": ObjectId(self.id)}, {"$set": {"type_id": ObjectId(new_type_id)}})
            self._doc["type_id"] = ObjectId(new_type_id)
        safe_operation(_update_type)
    
    def update(self, **kwargs):
        """Update contact with provided fields."""
        if kwargs:
            def _update():
                COLL.update_one({"_id": ObjectId(self.id)}, {"$set": kwargs})
                self._doc.update(kwargs)
            safe_operation(_update)

    # ------------------------------------------------------------------
    # Status helper (green / yellow / red)
    # ------------------------------------------------------------------

    def _threshold_days(self) -> tuple[int, bool]:
        """Return threshold days and whether ContactType is missing."""
        # Use embedded threshold from aggregation if available (eliminates N+1 query)
        if "_contact_type_threshold" in self._doc:
            # Check if contact_type data was found in aggregation
            has_missing_type = "_contact_type_missing" in self._doc
            return self._doc["_contact_type_threshold"], has_missing_type
        
        # Fallback to individual lookup (for backwards compatibility)
        ct = ContactType.get(self._doc["type_id"])
        if ct:
            return ct._doc["threshold_days"], False
        else:
            # Log critical data integrity issue
            current_app.logger.critical(
                f"Contact {self.id} references missing ContactType {self._doc['type_id']}"
            )
            return 7, True  # sensible fallback, mark as missing

    def status(self) -> str:
        """Return colour status string based on threshold_days."""
        thresh, _ = self._threshold_days()
        days_since = (datetime.utcnow().replace(tzinfo=timezone.utc) - self._doc["last_contact"]).days
        if days_since <= thresh:
            return "green"
        if days_since <= 2 * thresh:
            return "yellow"
        return "red"

    # ------------------------------------------------------------------
    # Model properties (for encapsulation)
    # ------------------------------------------------------------------

    @property
    def user_id(self) -> str:
        """Get the user ID that owns this contact."""
        return str(self._doc["user_id"])

    # ------------------------------------------------------------------
    # Serialisation
    # ------------------------------------------------------------------

    def to_dict(self, *, include_status: bool = True) -> Dict[str, Any]:
        data = {
            "id": self.id,
            "name": self._doc["name"],
            "type_id": str(self._doc["type_id"]),
            "last_contact": self._doc["last_contact"].isoformat(),
        }
        if include_status:
            data["status"] = self.status()
            # Include data integrity warning if ContactType is missing
            _, has_missing_type = self._threshold_days()
            if has_missing_type:
                data["data_integrity_warning"] = "Contact type not found - using default threshold"
        return data
