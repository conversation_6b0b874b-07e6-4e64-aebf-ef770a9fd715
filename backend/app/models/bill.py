from __future__ import annotations

"""Bill model helper wrapping the `bills` collection.

Supports one‑off and recurring entries.  Recurring rules follow RFC 5545 RRULE
syntax (e.g. "FREQ=MONTHLY;BYMONTHDAY=1" for rent on the 1st).  Helper
`upcoming_instances` expands at query‑time for the next *N* months without
materialising documents – front‑end receives dates already localised on render.
"""

from datetime import datetime, timedelta, timezone
from decimal import Decimal
from typing import Any, Dict, List, Optional

from bson.objectid import ObjectId
from dateutil.rrule import rrulestr
from dateutil.relativedelta import relativedelta
from flask import current_app

from app.extensions import mongo
from app.utils.database import safe_operation, DatabaseConnectionError

COLL = mongo.db.bills

# Expand 12 months into the future by default
_DEFAULT_HORIZON = 12  # months


class Bill:
    """Lightweight adapter around a single bill document."""

    def __init__(self, doc: Dict[str, Any]):
        self._doc = doc
        self.id: str = str(doc["_id"])

    # ---------------------------------------------------------------------
    # Creation helpers
    # ---------------------------------------------------------------------

    @classmethod
    def create(
        cls,
        *,
        user_id: str | ObjectId,
        title: str,
        amount: Optional[Decimal] = None,
        category: Optional[str] = None,
        notes: Optional[str] = None,
        due_date: Optional[datetime] = None,
        rrule: Optional[str] = None,
        start_date: Optional[datetime] = None,
    ) -> "Bill":
        """Insert a new bill (one‑off *or* recurring)."""

        if not (due_date or rrule):
            raise ValueError("Either due_date or rrule must be provided")
        if due_date and rrule:
            raise ValueError("Provide only one of due_date or rrule, not both")
        
        # Validate timezone awareness for consistency with Event model
        if due_date is not None and due_date.tzinfo is None:
            raise ValueError("due_date must be timezone-aware")
        if start_date is not None and start_date.tzinfo is None:
            raise ValueError("start_date must be timezone-aware")

        now = datetime.utcnow().replace(tzinfo=timezone.utc)
        doc: Dict[str, Any] = {
            "user_id": ObjectId(user_id),
            "title": title,
            "amount": amount,
            "category": category,
            "notes": notes,
            "due_date": due_date,
            "rrule": rrule,
            "start_date": start_date,
            "created_at": now,
            "updated_at": now,
        }
        def _insert():
            COLL.insert_one(doc)
            return cls(doc)
        return safe_operation(_insert)

    # ---------------------------------------------------------------------
    # Lookups
    # ---------------------------------------------------------------------

    @classmethod
    def get(cls, bill_id: str | ObjectId) -> "Bill | None":
        def _query():
            obj_id = ObjectId(bill_id) if isinstance(bill_id, str) else bill_id
            if (doc := COLL.find_one({"_id": obj_id})) is not None:
                return cls(doc)
            return None
        
        return safe_operation(_query, default_value=None)

    @classmethod
    def list_by_user(cls, user_id: str | ObjectId) -> List["Bill"]:
        def _query():
            cursor = COLL.find({"user_id": ObjectId(user_id)}).sort("title")
            return [cls(doc) for doc in cursor]
        
        return safe_operation(_query, default_value=[])

    # ---------------------------------------------------------------------
    # Instance operations
    # ---------------------------------------------------------------------

    def delete(self):
        def _delete():
            COLL.delete_one({"_id": ObjectId(self.id)})
        safe_operation(_delete)

    def update(self, **fields):
        if not fields:
            return
        fields["updated_at"] = datetime.utcnow().replace(tzinfo=timezone.utc)
        def _update():
            COLL.update_one({"_id": ObjectId(self.id)}, {"$set": fields})
            self._doc.update(fields)
        safe_operation(_update)

    # ---------------------------------------------------------------------
    # Recurrence expansion helper
    # ---------------------------------------------------------------------

    def upcoming_instances(
        self,
        *,
        horizon_months: int = _DEFAULT_HORIZON,
        from_dt: Optional[datetime] = None,
    ) -> List[datetime]:
        """Return due dates (UTC) within *horizon_months* from *from_dt*."""

        from_dt = from_dt or datetime.utcnow().replace(tzinfo=timezone.utc)
        to_dt = from_dt + relativedelta(months=horizon_months)

        # One‑off
        if self._doc.get("due_date"):
            due: datetime = self._doc["due_date"].replace(tzinfo=timezone.utc)
            return [due] if from_dt <= due <= to_dt else []

        # Recurring using RRULE
        rule_str: str = self._doc["rrule"]
        # Use explicit start_date if provided, otherwise fall back to created_at
        dtstart: datetime = (
            self._doc.get("start_date") or self._doc["created_at"]
        ).replace(tzinfo=timezone.utc)
        
        try:
            rule = rrulestr(rule_str, dtstart=dtstart)
            return list(rule.between(from_dt, to_dt, inc=True))
        except (ValueError, AttributeError) as e:
            # Log the error and return empty list for graceful degradation
            current_app.logger.error(f"Error parsing RRULE '{rule_str}' for bill {self.id}: {e}")
            return []

    # ---------------------------------------------------------------------
    # Model properties (for encapsulation)
    # ---------------------------------------------------------------------

    @property
    def user_id(self) -> str:
        """Get the user ID that owns this bill."""
        return str(self._doc["user_id"])

    # ---------------------------------------------------------------------
    # Serialisation
    # ---------------------------------------------------------------------

    def to_dict(self, *, include_instances: bool = False) -> Dict[str, Any]:
        data: Dict[str, Any] = {
            "id": self.id,
            "title": self._doc["title"],
            "amount": (str(self._doc["amount"]) if self._doc.get("amount") else None),
            "category": self._doc.get("category"),
            "notes": self._doc.get("notes"),
            "due_date": (self._doc["due_date"].isoformat() if self._doc.get("due_date") else None),
            "rrule": self._doc.get("rrule"),
            "start_date": (self._doc["start_date"].isoformat() if self._doc.get("start_date") else None),
        }
        if include_instances:
            data["instances"] = [d.isoformat() for d in self.upcoming_instances()]
        return data
