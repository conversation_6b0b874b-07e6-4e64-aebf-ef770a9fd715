from __future__ import annotations

"""Contact‑Type model helper.

Each *contact type* (e.g. Friend, Family, Client) defines a colour‑coding
threshold in **days** used by the dashboard to highlight how recently a user
has spoken with a contact of that type.
"""

from typing import Any, Dict, List, Optional

from bson.objectid import ObjectId

from app.extensions import mongo
from app.utils.database import safe_operation, DeletionPreventedError

COLL = mongo.db.contact_types


class ContactType:
    """Thin adapter around the `contact_types` collection."""

    def __init__(self, doc: Dict[str, Any]):
        self._doc = doc
        self.id: str = str(doc["_id"])

    # ------------------------------------------------------------------
    # CRUD helpers
    # ------------------------------------------------------------------

    @classmethod
    def create(cls, *, label: str, threshold_days: int) -> "ContactType":
        doc = {
            "label": label.strip(),
            "threshold_days": int(threshold_days),
        }
        def _insert():
            result = COLL.insert_one(doc)
            doc["_id"] = result.inserted_id
            return cls(doc)
        return safe_operation(_insert)

    @classmethod
    def get(cls, ct_id: str | ObjectId) -> "ContactType | None":
        def _query():
            obj_id = ObjectId(ct_id) if isinstance(ct_id, str) else ct_id
            if (doc := COLL.find_one({"_id": obj_id})) is not None:
                return cls(doc)
            return None
        return safe_operation(_query, default_value=None, reraise=False)

    @classmethod
    def by_label(cls, label: str) -> "ContactType | None":
        def _query():
            if (doc := COLL.find_one({"label": label.strip()})) is not None:
                return cls(doc)
            return None
        return safe_operation(_query, default_value=None, reraise=False)

    @classmethod
    def list_all(cls) -> List["ContactType"]:
        def _query():
            return [cls(d) for d in COLL.find().sort("label")]
        return safe_operation(_query, default_value=[])

    # ------------------------------------------------------------------
    # Instance mutators
    # ------------------------------------------------------------------

    def update(self, *, label: Optional[str] = None, threshold_days: Optional[int] = None) -> None:
        update: Dict[str, Any] = {}
        if label is not None:
            update["label"] = label.strip()
        if threshold_days is not None:
            update["threshold_days"] = int(threshold_days)
        if update:
            def _update():
                COLL.update_one({"_id": ObjectId(self.id)}, {"$set": update})
                self._doc.update(update)
            safe_operation(_update)

    def delete(self) -> None:
        """Delete contact type, but only if no contacts reference it."""
        def _delete():
            # Check if any contacts reference this contact type
            contacts_using_type = mongo.db.contacts.count_documents({"type_id": ObjectId(self.id)})
            if contacts_using_type > 0:
                raise DeletionPreventedError(f"Cannot delete contact type: {contacts_using_type} contacts still reference it")
            
            COLL.delete_one({"_id": ObjectId(self.id)})
        safe_operation(_delete)

    # ------------------------------------------------------------------
    # Serialisation
    # ------------------------------------------------------------------

    def to_dict(self) -> Dict[str, Any]:
        return {
            "id": self.id,
            "label": self._doc["label"],
            "threshold_days": self._doc["threshold_days"],
        }
