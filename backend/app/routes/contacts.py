from __future__ import annotations

"""Contacts CRUD and ping blueprint.

Routes
------
GET    /api/contacts                    → list contacts with status colours
POST   /api/contacts                    → create contact
PATCH  /api/contacts/<contact_id>       → update name / type / last_contact
POST   /api/contacts/<contact_id>/ping  → update last_contact to now
DELETE /api/contacts/<contact_id>       → delete contact
"""

from typing import Any, Dict, List

from bson import ObjectId
from flask import Blueprint, jsonify, request
from flask_login import current_user, login_required
from werkzeug.exceptions import BadRequest, NotFound

from app.models.contact import Contact
from app.models.contact_type import ContactType
from app.utils.auth import require_ownership
from app.utils.datetime import parse_iso8601
from app.utils.responses import json_error, json_success

bp = Blueprint("contacts", __name__, url_prefix="/api/contacts")


# ---------------------------------------------------------------------------
# Helpers
# ---------------------------------------------------------------------------




def _payload(c: Contact):
    return c.to_dict(include_status=True)


def _validate_type(type_id: str | None) -> ObjectId:
    if not type_id:
        raise BadRequest("type_id is required")
    ct = ContactType.get(type_id)
    if ct is None:
        raise BadRequest("Invalid type_id")
    return ObjectId(type_id)


# ---------------------------------------------------------------------------
# Routes
# ---------------------------------------------------------------------------


@bp.get("/")
@login_required
def list_contacts():
    contacts: List[Contact] = Contact.list_by_user(current_user.id)
    return json_success(data={"contacts": [_payload(c) for c in contacts]})


@bp.post("/")
@login_required
def create_contact():
    if not request.is_json:
        raise BadRequest("Request must be JSON")

    data: Dict[str, Any] = request.get_json(silent=True) or {}
    name = data.get("name", "").strip()
    if not name:
        return json_error("name is required")

    type_id_raw = data.get("type_id")
    try:
        type_id = _validate_type(type_id_raw)
    except BadRequest as exc:
        return json_error(str(exc), 422)

    last_raw = data.get("last_contact")
    last_dt = None
    if last_raw:
        try:
            last_dt = parse_iso8601(last_raw)
        except ValueError as exc:
            return json_error(str(exc), 422)

    contact = Contact.create(
        user_id=current_user.id,
        type_id=type_id,
        name=name,
        last_contact=last_dt,
    )
    return json_success(data={"contact": _payload(contact)}, code=201)


@bp.patch("/<contact_id>")
@login_required
@require_ownership(Contact)
def update_contact(contact_id: str, contact):

    data = request.get_json(silent=True) or {}
    update: Dict[str, Any] = {}

    if "name" in data:
        name = data["name"].strip()
        if not name:
            return json_error("name cannot be empty")
        update["name"] = name

    if "type_id" in data:
        try:
            update["type_id"] = _validate_type(data["type_id"])
        except BadRequest as exc:
            return json_error(str(exc), 422)

    if "last_contact" in data:
        try:
            update["last_contact"] = parse_iso8601(data["last_contact"])
        except ValueError as exc:
            return json_error(str(exc), 422)

    if update:
        contact.update(**update)

    return json_success(data={"contact": _payload(contact)})


@bp.post("/<contact_id>/ping")
@login_required
@require_ownership(Contact)
def ping_contact(contact_id: str, contact):
    contact.ping()
    return json_success(data={"contact": _payload(contact)})


@bp.delete("/<contact_id>")
@login_required
@require_ownership(Contact)
def delete_contact(contact_id: str, contact):
    contact.delete()
    return {"status": "deleted"}, 204
