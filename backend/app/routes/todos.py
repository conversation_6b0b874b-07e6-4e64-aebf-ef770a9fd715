from __future__ import annotations

"""To-Dos CRUD blueprint.

All endpoints are authenticated and scoped to the single user. Supports
basic CRUD operations for todo items with completion status.

Routes
------
GET    /api/todos                 → list all todos
POST   /api/todos                 → create todo
PATCH  /api/todos/<todo_id>       → partial update
DELETE /api/todos/<todo_id>       → hard delete
"""

from typing import Any, Dict, List, Optional

from bson import ObjectId
from flask import Blueprint, jsonify, request
from flask_login import current_user, login_required
from werkzeug.exceptions import BadRequest, NotFound

from app.models.todo import Todo
from app.utils.auth import require_ownership
from app.utils.responses import json_error, json_success

bp = Blueprint("todos", __name__, url_prefix="/api/todos")


# ---------------------------------------------------------------------------
# Helpers
# ---------------------------------------------------------------------------




def _validate_todo_data(data: Dict[str, Any]) -> Dict[str, Any]:
    """Validate and normalize todo data."""
    validated = {}
    
    if "text" in data:
        if not isinstance(data["text"], str) or not data["text"].strip():
            raise BadRequest("Text is required and must be non-empty string")
        validated["text"] = data["text"].strip()
    
    return validated


# ---------------------------------------------------------------------------
# Routes
# ---------------------------------------------------------------------------


@bp.get("")
@login_required
def list_todos():
    """List all todos for the current user."""
    todos = Todo.list_by_user(current_user.id)
    return json_success(data={"todos": [todo.to_dict() for todo in todos]})


@bp.post("")
@login_required
def create_todo():
    """Create a new todo."""
    if not request.is_json:
        raise BadRequest("Request must be JSON")
    
    data = request.get_json(silent=True) or {}
    validated = _validate_todo_data(data)
    if "text" not in validated:
        raise BadRequest("Text is required")
    
    todo = Todo.create(
        user_id=current_user.id,
        text=validated["text"]
    )
    
    return json_success(data={"todo": todo.to_dict()}, code=201)


@bp.patch("/<todo_id>")
@login_required
@require_ownership(Todo)
def update_todo(todo_id: str, todo):
    """Update an existing todo."""
    data = request.get_json(silent=True) or {}
    
    # Build update payload for single database operation
    update_fields = {}
    
    # Handle text updates
    if "text" in data:
        validated = _validate_todo_data(data)
        if "text" in validated:
            update_fields["text"] = validated["text"]
    
    # Handle completion status (allow toggle)
    if "completed" in data:
        update_fields["completed"] = data["completed"]
    
    # Perform single database update if any fields were provided
    if update_fields:
        todo.update(**update_fields)
    
    return json_success(data={"todo": todo.to_dict()})


@bp.delete("/<todo_id>")
@login_required
@require_ownership(Todo)
def delete_todo(todo_id: str, todo):
    """Delete a todo."""
    todo.delete()
    return {"status": "deleted"}, 204