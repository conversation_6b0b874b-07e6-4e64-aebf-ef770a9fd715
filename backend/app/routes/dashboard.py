"""Blueprint aggregator.

Registers every API blueprint with the Flask app. Imports live within the
function to avoid circular dependencies during application startup.
"""

from flask import Flask


def register_blueprints(app: Flask) -> None:  # noqa: WPS231
    """Attach all API blueprints to *app*."""

    # --- Auth --------------------------------------------------------------
    from .auth import bp as auth_bp  # type: ignore
    app.register_blueprint(auth_bp)

    # --- Profile -----------------------------------------------------------
    from .profile import bp as profile_bp  # type: ignore
    app.register_blueprint(profile_bp)

    # --- Bills & Vacations -------------------------------------------------
    from .bills import bp as bills_bp  # type: ignore
    app.register_blueprint(bills_bp)

    # --- Events / Appointments --------------------------------------------
    from .events import bp as events_bp  # type: ignore
    app.register_blueprint(events_bp)

    # --- To‑Dos ------------------------------------------------------------
    from .todos import bp as todos_bp  # type: ignore
    app.register_blueprint(todos_bp)

    # --- Contact Types -----------------------------------------------------
    from .contact_types import bp as contact_types_bp  # type: ignore
    app.register_blueprint(contact_types_bp)

    # --- Contacts ----------------------------------------------------------
    from .contacts import bp as contacts_bp  # type: ignore
    app.register_blueprint(contacts_bp)

    # --- Weather Proxy -----------------------------------------------------
    from .weather import bp as weather_bp  # type: ignore
    app.register_blueprint(weather_bp)

    # --- Dashboard Aggregation --------------------------------------------
    from .dashboard_api import bp as dashboard_bp  # type: ignore
    app.register_blueprint(dashboard_bp)

    # Future blueprints (settings, layout, etc.) will be appended as their
    # implementation files are delivered.
