from __future__ import annotations

"""Bills & Vacations CRUD blueprint.

All endpoints are authenticated and scoped to the single user.  Recurrence
rules follow RFC 5545; the helper model expands upcoming instances on demand.

Routes
------
GET    /api/bills                 → list all bills (with next instances)
POST   /api/bills                 → create bill (one‑off or recurring)
PATCH  /api/bills/<bill_id>       → partial update
DELETE /api/bills/<bill_id>       → hard delete
"""

from decimal import Decimal, InvalidOperation
from typing import Any, Dict, List, Optional

from bson import ObjectId
from flask import Blueprint, jsonify, request
from flask_login import current_user, login_required
from werkzeug.exceptions import BadRequest, NotFound

from app.models.bill import Bill
from app.utils.auth import require_ownership
from app.utils.datetime import parse_iso8601
from app.utils.responses import json_error, json_success

bp = Blueprint("bills", __name__, url_prefix="/api/bills")


# ---------------------------------------------------------------------------
# Helpers
# ---------------------------------------------------------------------------




def _parse_decimal(value: Any) -> Optional[Decimal]:
    if value in (None, ""):
        return None
    try:
        return Decimal(str(value))
    except InvalidOperation:
        raise BadRequest("Invalid amount format")


# ---------------------------------------------------------------------------
# Serialisation helpers
# ---------------------------------------------------------------------------


def _bill_payload(bill: Bill):
    return bill.to_dict(include_instances=True)


# ---------------------------------------------------------------------------
# Routes
# ---------------------------------------------------------------------------


@bp.get("/")
@login_required
def list_bills():
    bills: List[Bill] = Bill.list_by_user(current_user.id)
    return json_success(data={"bills": [_bill_payload(b) for b in bills]})


@bp.post("/")
@login_required
def create_bill():
    if not request.is_json:
        raise BadRequest("Request must be JSON")

    data: Dict[str, Any] = request.get_json(silent=True) or {}
    title = data.get("title", "").strip()
    if not title:
        return json_error("Title is required")

    amount = _parse_decimal(data.get("amount"))
    category = (data.get("category") or "").strip() or None
    notes = (data.get("notes") or "").strip() or None
    due_date_raw = data.get("due_date")
    rrule = (data.get("rrule") or "").strip() or None

    if bool(due_date_raw) == bool(rrule):
        return json_error("Provide exactly one of due_date or rrule")

    due_date = None
    if due_date_raw:
        try:
            due_date = parse_iso8601(due_date_raw)
        except ValueError:
            return json_error("Invalid due_date format (use ISO 8601)")

    bill = Bill.create(
        user_id=current_user.id,
        title=title,
        amount=amount,
        category=category,
        notes=notes,
        due_date=due_date,
        rrule=rrule,
    )
    return json_success(data={"bill": _bill_payload(bill)}, code=201)


@bp.patch("/<bill_id>")
@login_required
@require_ownership(Bill)
def update_bill(bill_id: str, bill):

    data = request.get_json(silent=True) or {}
    update: Dict[str, Any] = {}

    if "title" in data:
        title = data["title"].strip()
        if not title:
            return json_error("Title cannot be empty")
        update["title"] = title

    if "amount" in data:
        update["amount"] = _parse_decimal(data["amount"])

    if "category" in data:
        update["category"] = (data["category"].strip() or None)

    if "notes" in data:
        update["notes"] = (data["notes"].strip() or None)

    if {"due_date", "rrule"} & data.keys():
        # Validate mutual exclusivity
        due_raw = data.get("due_date")
        rr = (data.get("rrule") or "").strip() or None
        if bool(due_raw) and bool(rr):
            return json_error("Provide only one of due_date or rrule")
        if due_raw:
            try:
                update["due_date"] = parse_iso8601(due_raw)
                update["rrule"] = None
            except ValueError:
                return json_error("Invalid due_date format")
        elif rr is not None:
            update["rrule"] = rr
            update["due_date"] = None

    if update:
        bill.update(**update)

    return json_success(data={"bill": _bill_payload(bill)})


@bp.delete("/<bill_id>")
@login_required
@require_ownership(Bill)
def delete_bill(bill_id: str, bill):
    bill.delete()
    return {"status": "deleted"}, 204
