from __future__ import annotations

"""Authentication blueprint providing login, logout, and current‑user lookup.

All endpoints are JSON‑only and stateless beyond the secure session cookie
managed by <PERSON><PERSON><PERSON>‑<PERSON><PERSON>.  Error messages are returned in a consistent format
for easy consumption by the React front‑end.
"""

from flask import Blueprint, jsonify, request
from flask_login import current_user, login_required, login_user, logout_user
from werkzeug.exceptions import BadRequest, Unauthorized

from app.extensions import limiter, login_manager
from app.models.user import User
from app.utils.responses import json_error, json_success


bp = Blueprint("auth", __name__, url_prefix="/api/auth")


# ---------------------------------------------------------------------------
# Flask‑Login integration
# ---------------------------------------------------------------------------


@login_manager.user_loader  # type: ignore[misc]
def _load_user(user_id: str):  # pragma: no cover
    return User.get(user_id)


# ---------------------------------------------------------------------------
# Helpers
# ---------------------------------------------------------------------------




# ---------------------------------------------------------------------------
# Routes
# ---------------------------------------------------------------------------


@bp.post("/login")
@limiter.limit("5 per minute")  # extra guard beyond global default
def login():
    """Validate credentials and start a session."""

    if not request.is_json:
        raise BadRequest("Request must be JSON")

    data = request.get_json(silent=True) or {}
    username = data.get("username", "").strip()
    password = data.get("password", "")

    if not username or not password:
        raise BadRequest("Username and password required")

    user = User.by_username(username)
    if user is None or not user.verify_password(password):
        # Do not leak which field is wrong
        raise Unauthorized("Invalid credentials")

    login_user(user)
    return json_success(data={"user": user.to_dict()})


@bp.post("/logout")
@login_required
def logout():
    logout_user()
    return {"status": "logged_out"}, 204


@bp.get("/me")
@login_required
def me():
    return json_success(data={"user": current_user.to_dict()})
