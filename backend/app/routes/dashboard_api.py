from __future__ import annotations

"""Dashboard aggregation endpoint.

Provides a single endpoint that aggregates data from all widget sources
for efficient dashboard loading with parallel data fetching.

Route
-----
GET /api/dashboard    → aggregated dashboard data
"""

import concurrent.futures
from flask import Blueprint, jsonify, current_app
from flask_login import current_user, login_required
from pymongo.errors import ServerSelectionTimeoutError, ConnectionFailure

from app.models.bill import Bill
from app.models.contact import Contact
from app.models.event import Event
from app.models.todo import Todo
from app.services.weather import get_weather
from app.utils.responses import json_success

bp = Blueprint("dashboard", __name__, url_prefix="/api/dashboard")


def safe_dashboard_fetch(operation_func, data_type: str):
    """Wrapper for dashboard data fetching with consistent error handling."""
    try:
        return operation_func()
    except (ServerSelectionTimeoutError, ConnectionFailure) as e:
        current_app.logger.error(f"Database connection failed while fetching {data_type} for user {current_user.id}: {str(e)}")
        return {"error": f"Unable to load {data_type} due to connection issue"}
    except Exception as e:
        current_app.logger.error(f"Unexpected error fetching {data_type} for user {current_user.id}: {str(e)}")
        raise


@bp.get("")
@login_required
def dashboard():
    """Aggregate data from all dashboard widgets using parallel data fetching."""
    
    def fetch_bills():
        return safe_dashboard_fetch(
            lambda: [bill.to_dict(include_instances=True) for bill in Bill.list_by_user(current_user.id)],
            "bills"
        )
    
    def fetch_contacts():
        return safe_dashboard_fetch(
            lambda: [contact.to_dict() for contact in Contact.list_by_user(current_user.id)],
            "contacts"
        )
    
    def fetch_events():
        return safe_dashboard_fetch(
            lambda: [event.to_dict(include_instances=True) for event in Event.list_by_user(current_user.id)],
            "events"
        )
    
    def fetch_todos():
        return safe_dashboard_fetch(
            lambda: [todo.to_dict() for todo in Todo.list_by_user(current_user.id)],
            "todos"
        )
    
    def fetch_weather():
        user_weather_city = current_user.weather_city
        return get_weather(user_weather_city)
    
    # Execute all data fetching operations in parallel using ThreadPoolExecutor
    with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:
        future_bills = executor.submit(fetch_bills)
        future_contacts = executor.submit(fetch_contacts)
        future_events = executor.submit(fetch_events)
        future_todos = executor.submit(fetch_todos)
        future_weather = executor.submit(fetch_weather)
        
        # Collect results with timeout and graceful fallbacks
        def get_result_with_fallback(future, name, default_value):
            try:
                return future.result(timeout=10)
            except concurrent.futures.TimeoutError:
                current_app.logger.warning(f"Dashboard {name} data fetch timed out after 10 seconds")
                return {"error": f"Unable to load {name} data (timeout)"}
            except Exception as e:
                current_app.logger.error(f"Dashboard {name} data fetch failed: {str(e)}")
                return default_value
        
        bills = get_result_with_fallback(future_bills, "bills", [])
        contacts = get_result_with_fallback(future_contacts, "contacts", [])
        events = get_result_with_fallback(future_events, "events", [])
        todos = get_result_with_fallback(future_todos, "todos", [])
        weather_data = get_result_with_fallback(future_weather, "weather", {"error": "Weather data unavailable"})
    
    return json_success(data={
        "weather": weather_data,
        "bills": bills,
        "contacts": contacts, 
        "events": events,
        "todos": todos
    })