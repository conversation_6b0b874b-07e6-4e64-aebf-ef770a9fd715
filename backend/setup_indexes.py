#!/usr/bin/env python3
"""MongoDB index setup script for Personal Organizer application.

This script creates performance-optimized indexes for the main collections.
Run this after setting up the database for the first time or after deploying
to production.

Usage:
    python setup_indexes.py
"""

import os
import sys
from pymongo import MongoClient, ASCENDING

# Import Flask app to access configuration
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
from app import create_app

def setup_indexes():
    """Create MongoDB indexes for optimal query performance."""
    
    # Create Flask app to access configuration (ensures single source of truth)
    app = create_app()
    
    with app.app_context():
        # Get configuration from Flask app (same source as runtime)
        mongo_uri = app.config['MONGO_URI']
        weather_ttl = app.config['WEATHER_CACHE_TTL']
    
    try:
        client = MongoClient(mongo_uri)
        db = client.get_default_database()
        
        print(f"Connected to MongoDB: {mongo_uri}")
        print("Setting up indexes...")
        
        # Users collection - unique index on username for fast lookups and uniqueness
        users_result = db.users.create_index([("username", ASCENDING)], unique=True, background=True)
        print(f"Users collection: created index on 'username' - {users_result}")
        
        # Bills collection - index on user_id for fast user-specific queries
        bills_result = db.bills.create_index([("user_id", ASCENDING)], background=True)
        print(f"Bills collection: created index on 'user_id' - {bills_result}")
        
        # Contacts collection - index on user_id for fast user-specific queries
        contacts_result = db.contacts.create_index([("user_id", ASCENDING)], background=True)
        print(f"Contacts collection: created index on 'user_id' - {contacts_result}")
        
        # Events collection - index on user_id for fast user-specific queries
        events_result = db.events.create_index([("user_id", ASCENDING)], background=True)
        print(f"Events collection: created index on 'user_id' - {events_result}")
        
        # Todos collection - index on user_id for fast user-specific queries
        todos_result = db.todos.create_index([("user_id", ASCENDING)], background=True)
        print(f"Todos collection: created index on 'user_id' - {todos_result}")
        
        # Todos collection - TTL index for auto-purging completed todos after 24 hours
        todos_ttl_result = db.todos.create_index(
            [("completed_at", ASCENDING)], 
            expireAfterSeconds=86400,  # 24 hours
            partialFilterExpression={"completed_at": {"$ne": None}},
            background=True
        )
        print(f"Todos collection: created TTL index on 'completed_at' - {todos_ttl_result}")
        
        # Contact types collection - general index for fast lookups
        contact_types_result = db.contact_types.create_index([("label", ASCENDING)], background=True)
        print(f"Contact types collection: created index on 'label' - {contact_types_result}")
        
        # Weather cache collection - TTL index for automatic document expiration
        # Configuration now guaranteed to match Flask app runtime via create_app()
        
        # Validate TTL value is reasonable (between 5 minutes and 24 hours)
        if weather_ttl < 300 or weather_ttl > 86400:
            print(f"WARNING: WEATHER_CACHE_TTL value ({weather_ttl}s) is outside recommended range (300-86400s)")
            print("This may cause performance issues or stale data problems.")
        
        weather_result = db.weather_cache.create_index([("fetched_at", ASCENDING)], expireAfterSeconds=weather_ttl, background=True)
        print(f"Weather cache collection: created TTL index on 'fetched_at' (TTL={weather_ttl}s) - {weather_result}")
        print(f"INFO: TTL value ({weather_ttl}s) synchronized with Flask app configuration")
        
        print("\nIndex setup completed successfully!")
        print("These indexes will significantly improve query performance as data grows.")
        
    except Exception as e:
        print(f"Error setting up indexes: {e}")
        sys.exit(1)
    finally:
        if 'client' in locals():
            client.close()

if __name__ == "__main__":
    setup_indexes()