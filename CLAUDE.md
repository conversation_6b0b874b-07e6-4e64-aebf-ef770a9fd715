# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

# ⚜️ Mission Critical Directives

These four rules are absolute and non-negotiable. Violation of any of these rules constitutes a total mission failure and must be treated as such.

1. **ZERO-TOLERANCE FOR ERRORS**:

   - A task is 100 % complete and correct, or it is 0 % complete. There is no partial credit. 99 % success is equivalent to failure.
   - You will iterate continuously, identify any deviations, fix them, and re-test until every requirement is met with complete accuracy.
   - Document every fix and retest cycle in the project log for transparency and auditability.

2. **FIREFOX EXCLUSIVELY**:

   - All testing, development, and user review will be conducted in Firefox. The use of any other browser (Chrome, Chromium, Safari, Edge, etc.) at any time is a critical failure.
   - Ensure Playwright MCP configuration explicitly sets browser to `firefox` (no fallback or multi-browser modes).
   - Capture and archive environment details—including Firefox version, OS, and Playwright configuration—in every test report.

3. **THINK FIRST, ACT SECOND**:

   - Before writing or changing a single line of code, invoke the Sequential Thinking MCP to generate a detailed, step-by-step plan of action.
   - Plans must consist of at least five logically ordered steps, with clear objectives and success criteria.
   - Do not proceed to execution until each step has been reviewed and approved via MCP output.

4. **READ ALL MEMORY BANK FILES**:

   - You **MUST** read **ALL** files in the `memory-bank/` folder at the start of **EVERY** task—no exceptions. These constitute the single source of truth for project history and context.
   - If the `memory-bank/` folder is missing, you must create it immediately and scaffold all **required core files** before proceeding:
     - `projectbrief.md`
     - `productContext.md`
     - `systemPatterns.md`
     - `techContext.md`
     - `activeContext.md`
     - `progress.md`
   - Log the creation event in the project diary with timestamps and rationale.

---

# General Instructions
- Do not hallucinate or fabricate facts.
- Maintain this role unless explicitly instructed otherwise.
- After each user query, respond in clear, logical steps; keep each draft thought ≤ 5 words.
- Visualize outcomes, explore options slowly, and plan carefully.
- User expects world-class graphics and visual quality in applications, not basic or poor-looking interfaces. Verify this during testing.

# Critical Workflow Rules
- **INITIALIZE MEMORY (IF NEEDED)**: If the project folder lacks a memory bank, create it before any other action.
- **SEQUENTIAL THINKING FIRST**: After EVERY user interaction, IMMEDIATELY call the Sequential Thinking MCP.
- **SEQUENTIAL THINKING ON EACH TODO ITEM**: *Before executing each TODO, invoke Sequential Thinking MCP for that specific item.*
- **ON ANY ERROR**: Always call Sequential Thinking MCP AND review the Augment Context Engine whenever a command fails.
- **REVIEW GUIDELINES**: Re-read these guidelines after EACH user interaction to ensure compliance.
- Never save installation summaries as files.
- Conduct thorough research with Context7 and other tools before every project.
- For maximum efficiency, invoke independent tools simultaneously rather than sequentially.

# User Requirements
- Provide comprehensive Playwright tests (Firefox-only) with screenshots.
- Implement recurring features consistently across modules (activities mirror financial transactions with checkbox, frequency options, backend logic, and calendar integration).
- Absolutely never save installation summaries as files.
- User expects strict adherence to the MANDATORY 3-STEP PATTERN: draft 5-8 TODOs, execute with Sequential Thinking for each, then complete with Memory Bank review/update cycle.

# Process Management
- Always check running processes with list-processes before launching new waiting processes.
- Kill unnecessary processes first.
- Use `wait=false` for background servers to prevent "Cannot launch another waiting process" errors.

# Mandatory 3-Step Pattern
- Draft a TODO list (5–8 items)
   • At the start of each work cycle, write **between five (5) and eight (5) ordered TODO items (`TODO-1`, `TODO-2`, …).
- Execute the TODOs sequentially
   • *For each TODO item:*
     1. **Call Sequential Thinking MCP** for that item.
     2. Perform the action and mark the item done.
- When the last TODO is complete (and the list had 5–8 items), run the 3-STEP PATTERN — no omissions, no re-ordering, no exceptions.
   - **Step 1 — Review Memory Bank (READ)**
   - **Step 2 — Sequential Thinking (MCP)**
   - **Step 3 — Update Memory Bank (WRITE)**
- Label the checkpoint
   • Prepend the three-step block with `TODO LIST COMPLETE — MANDATORY 3-STEP PATTERN:`
- Loop
   • Immediately draft a fresh 5–8-item TODO list and repeat from Step 2.

**Strict adherence is required.** Failure to execute Sequential Thinking for each TODO and the 3-step pattern after every completed list is unacceptable.

## Development Commands

### Backend Development
```bash
# Navigate to backend directory
cd backend

# Create and activate virtual environment
python3 -m venv venv
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt

# Environment setup
cp .env.example .env  # Edit with your values

# Run development server
flask --app app run -p 8000
# OR with debug mode
FLASK_ENV=development flask --app app run -p 8000

# Run production server
gunicorn -c gunicorn_config.py wsgi:app

# Database index setup
python setup_indexes.py
```

### Frontend Development
```bash
# Navigate to frontend directory
cd frontend

# Install dependencies
npm install

# Environment setup
cp .env.example .env  # Edit VITE_BACKEND_URL if needed

# Run development server
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview
```

### Testing
```bash
# Run Playwright tests (Firefox only)
npx playwright test

# Run specific test file
npx playwright test test-login-comprehensive.js

# Run with UI mode
npx playwright test --ui

# Install browsers (if needed)
npx playwright install firefox
```

### Code Quality
```bash
# Backend linting (if available)
cd backend
flake8 .
black .

# Python testing
pytest
```

## Architecture Overview

### Full-Stack Architecture
- **Frontend**: React 18 SPA with Vite build system
- **Backend**: Flask 3.x REST API with factory pattern
- **Database**: MongoDB with TTL indexes
- **Authentication**: Flask-Login with session cookies
- **Deployment**: Gunicorn + systemd service

### Flask Application Structure
```
backend/app/
├── __init__.py           # Application factory (create_app)
├── config.py            # Environment-driven configuration classes
├── extensions.py        # Flask extension initialization
├── models/             # SQLAlchemy/MongoDB models
├── routes/             # Blueprint-based route organization
├── services/           # Business logic services
└── utils/              # Shared utilities and helpers
```

### Key Backend Patterns
- **Factory Pattern**: `create_app()` function creates configured Flask instances
- **Blueprint Organization**: Routes organized by feature (auth, bills, contacts, etc.)
- **Configuration Classes**: Environment-specific configs (Development/Production)
- **Extension Initialization**: Centralized in `extensions.py`
- **Error Handling**: Centralized HTTP exception handling with JSON responses

### Frontend Architecture
```
frontend/src/
├── components/         # Reusable UI components
├── pages/             # Route-level page components
├── context/           # React Context providers (AuthContext)
├── api/               # API client utilities
├── hooks/             # Custom React hooks
└── routes/            # React Router configuration
```

### Key Frontend Patterns
- **Context API**: `AuthContext` for global authentication state
- **Component Composition**: Card-based UI components for dashboard widgets
- **Custom Hooks**: `useAuth` hook for authentication logic
- **Proxy Configuration**: Vite proxy forwards `/api` requests to Flask backend

## Database Models

### Core Collections
- **Users**: Authentication and user preferences
- **Todos**: Task management with completion status
- **Contacts**: Contact management with categorization
- **Bills**: Financial tracking with recurring logic
- **Events**: Calendar events with RRULE support
- **Weather**: Cached weather data with TTL

### Key Model Patterns
- **TTL Indexes**: Automatic cleanup of old todos and cached weather data
- **Relationship Mappings**: ContactType references in contacts
- **DateTime Handling**: UTC timezone-aware datetime objects
- **ObjectId Validation**: Proper MongoDB ObjectId handling

## Environment Configuration

### Required Backend Environment Variables
```bash
# Core Flask settings
SECRET_KEY=your-64-char-secret-key
FLASK_ENV=development  # or production
MONGO_URI=mongodb://localhost:27017/personal_organizer

# CORS configuration
CORS_ALLOWED_ORIGINS=http://localhost:5173

# Weather integration
WEATHER_CACHE_TTL=7200  # seconds (2 hours)
```

### Required Frontend Environment Variables
```bash
# Backend API endpoint
VITE_BACKEND_URL=http://127.0.0.1:8000
```

## Testing Strategy

### Playwright Configuration
- **Browser**: Firefox exclusively (mission critical requirement)
- **Test Files**: Located in project root matching `test-*.js` pattern
- **Screenshots**: Captured on both success and failure
- **Reports**: HTML reports generated in `playwright-report/`

### Key Testing Patterns
- **Page Object Model**: Organized test interactions
- **Screenshot Documentation**: Visual regression testing
- **Session Testing**: Login/logout flows with cookie persistence
- **Responsive Testing**: Multiple viewport sizes
- **API Integration**: Backend API endpoint validation

## Deployment Patterns

### Development Deployment
1. Start MongoDB service
2. Start Flask backend on port 8000
3. Start Vite frontend on port 5173
4. Vite proxy handles API requests to backend

### Production Deployment
1. Use Gunicorn with `gunicorn_config.py`
2. systemd service for process management
3. Cloudflare Tunnel for HTTPS exposure
4. Build frontend with `npm run build`
5. Serve static files via Nginx or CDN

## Key Integration Points

### Authentication Flow
- Flask-Login manages server-side sessions
- JWT tokens stored in httpOnly cookies
- CSRF protection enabled
- Session validation on each API request

### API Communication
- RESTful API design with `/api` prefix
- Consistent JSON response format
- Centralized error handling
- Proper HTTP status codes

### Widget System
- Dashboard aggregates data from multiple endpoints
- Individual widget error handling with graceful fallbacks
- Parallel data fetching for performance
- Real-time updates via React Query

## Important Notes

- **Memory Bank**: Always read all files in `memory-bank/` before starting any task
- **Testing**: All testing must be done in Firefox exclusively
- **Sessions**: Use demo/demo123 credentials for testing
- **Ports**: Backend runs on 8000, frontend on 5173
- **CSS**: TailwindCSS with custom brand colors and dark mode support
- **Performance**: MongoDB indexes are critical for production performance