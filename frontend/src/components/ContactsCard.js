import React from "react";
import PropTypes from "prop-types";
import axios from "axios";
import { useQueryClient } from "@tanstack/react-query";
import { UserRound, Mail } from "lucide-react";
import clsx from "clsx";

const statusColor = {
  green: "bg-green-500",
  yellow: "bg-yellow-400",
  red: "bg-red-500",
};

export default function ContactsCard({ items }) {
  const queryClient = useQueryClient();

  async function handlePing(contact) {
    try {
      // Optimistic update - update the contact status immediately
      queryClient.setQueryData(["dashboard"], (oldData) => {
        if (!oldData) return oldData;
        
        return {
          ...oldData,
          contacts: oldData.contacts.map(c => 
            c.id === contact.id 
              ? { ...c, status: "green", last_contact: new Date().toISOString() }
              : c
          )
        };
      });

      // Make the API call
      await axios.post(`/api/contacts/${contact.id}/ping`);
      
      // Invalidate and refetch to get the actual server state
      await queryClient.invalidateQueries({
        queryKey: ["dashboard"],
        exact: true,
        refetchType: "active"
      });
      
    } catch (error) {
      // If the API call fails, invalidate to revert optimistic update
      console.error("Failed to ping contact:", error);
      queryClient.invalidateQueries({
        queryKey: ["dashboard"],
        exact: true,
        refetchType: "active"
      });
    }
  }

  return (
    <div className="flex h-full flex-col">
      <h3 className="mb-2 text-lg font-semibold">Contacts</h3>
      <div className="flex-1 space-y-1 overflow-y-auto pr-1">
        {items.length === 0 && <p className="text-sm text-gray-500">No contacts yet.</p>}
        {items.map((c) => (
          <div
            key={c.id}
            className="flex items-center justify-between rounded py-1 px-2 hover:bg-gray-50 dark:hover:bg-gray-700"
          >
            <div className="flex items-center gap-2 overflow-hidden">
              <span
                className={clsx("h-2.5 w-2.5 rounded-full", statusColor[c.status] || "bg-gray-400")}
                aria-label={c.status}
              />
              <UserRound size={16} className="shrink-0 text-brand-500" />
              <span className="truncate text-sm font-medium">{c.name}</span>
            </div>
            <button
              onClick={() => handlePing(c)}
              className="rounded-full p-1 text-gray-500 hover:text-brand-600 dark:hover:text-brand-400"
              aria-label="Ping contact"
            >
              <Mail size={16} />
            </button>
          </div>
        ))}
      </div>
    </div>
  );
}

ContactsCard.propTypes = {
  items: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.string.isRequired,
      name: PropTypes.string.isRequired,
      status: PropTypes.oneOf(["green", "yellow", "red"]).isRequired,
    })
  ).isRequired,
};
