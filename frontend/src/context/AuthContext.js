import React, { createContext, useContext, useEffect, useState } from "react";
import axios from "axios";
import PropTypes from "prop-types";

// Configure axios to send cookies with all requests
axios.defaults.withCredentials = true;

// ---------------------------------------------------------------------------
// Context setup
// ---------------------------------------------------------------------------

const AuthContext = createContext({
  isAuthenticated: false,
  user: null,
  loading: true,
  error: null,
  login: async () => {},
  logout: async () => {},
  clearError: () => {},
});

export function AuthProvider({ children }) {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Fetch session on initial mount
  useEffect(() => {
    const controller = new AbortController();
    
    async function fetchSession() {
      try {
        const { data } = await axios.get("/api/auth/me", {
          signal: controller.signal,
          timeout: 10000, // 10 second timeout
        });
        setUser(data.user);
        setError(null);
      } catch (err) {
        if (err.name === 'AbortError' || err.code === 'ERR_CANCELED') {
          return; // Request was cancelled, don't update state
        }
        
        // Differentiate between authentication errors and network errors
        if (err.response?.status === 401) {
          // Not logged in or session expired - this is expected
          setUser(null);
          setError(null);
        } else if (err.code === 'ECONNABORTED' || err.message.includes('timeout')) {
          setError('Connection timeout. Please check your internet connection.');
          setUser(null);
        } else if (!err.response) {
          setError('Unable to connect to server. Please try again later.');
          setUser(null);
        } else {
          setError('An unexpected error occurred. Please try again.');
          setUser(null);
        }
      } finally {
        setLoading(false);
      }
    }
    
    fetchSession();
    
    // Cleanup function to cancel request if component unmounts
    return () => {
      controller.abort();
    };
  }, []);

  // -----------------------------------------------------------------------
  // Actions
  // -----------------------------------------------------------------------

  async function login(username, password) {
    try {
      setError(null); // Clear any previous errors
      const { data } = await axios.post("/api/auth/login", { username, password });
      setUser(data.user);
      return { success: true };
    } catch (err) {
      let errorMessage = 'Login failed. Please try again.';
      
      if (err.response?.status === 401) {
        errorMessage = 'Invalid username or password.';
      } else if (err.response?.status === 429) {
        errorMessage = 'Too many login attempts. Please try again later.';
      } else if (err.code === 'ECONNABORTED' || err.message.includes('timeout')) {
        errorMessage = 'Connection timeout. Please check your internet connection.';
      } else if (!err.response) {
        errorMessage = 'Unable to connect to server. Please try again later.';
      }
      
      setError(errorMessage);
      return { success: false, error: errorMessage };
    }
  }

  async function logout() {
    try {
      setError(null);
      await axios.post("/api/auth/logout");
      setUser(null);
      return { success: true };
    } catch (err) {
      // Even if logout API fails, clear user state locally
      setUser(null);
      
      let errorMessage = 'Logout completed, but there was a server error.';
      if (!err.response) {
        errorMessage = 'Logout completed, but unable to notify server.';
      }
      
      setError(errorMessage);
      return { success: false, error: errorMessage };
    }
  }

  function clearError() {
    setError(null);
  }

  const value = {
    isAuthenticated: Boolean(user),
    user,
    loading,
    error,
    login,
    logout,
    clearError,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

AuthProvider.propTypes = {
  children: PropTypes.node.isRequired,
};

export function useAuth() {
  return useContext(AuthContext);
}
